import request from '@/utils/request';
import endpoints from './endpoints';

// ------------------------------------------------------------------------------------------
const { activity } = endpoints;

// 活动列表
export const activityPage = (data) => request.post(activity.activityPage, data);
// 活动详情-for显示
export const activityDetail2 = (data) => request.post(activity.activityDetail2, data);
  // 
// 保存活动(店员)分享浏览记录关系  "shareUserId": 1,"activityId": 1
export const saveActivityShare = (data) => request.post(activity.saveActivityShare, data);
// 保存活动的优惠券(好友)分享浏览记录关系 "shareUserId": 1, "couponId": 1
export const saveActivityCouponShare = (data) => request.post(activity.saveActivityCouponShare, data);