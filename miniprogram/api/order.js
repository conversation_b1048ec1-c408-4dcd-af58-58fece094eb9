import request from '@/utils/request';
import endpoints from './endpoints';

// ------------------------------------------------------------------------------------------
const { order, pay } = endpoints;

// 创建活动订单并发起预支付
export const createActivityOrder = (data) => request.post(order.createActivityOrder, data);
// 活动订单列表
export const orderList = (data) => request.post(order.orderList, data);
// 活动订单详情
export const orderDetail = (data) => request.post(order.orderDetail, data);
// 订单退款
export const orderRefund = (data) => request.post(order.orderRefund, data);
// 收银台（订单发起预支付）
export const cashier = (data) => request.post(pay.cashier, data);
// 订单支付状态
export const payStatus = (data) => request.post(pay.payStatus, data);
