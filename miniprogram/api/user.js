import request from '@/utils/request';
import endpoints from './endpoints';

// ------------------------------------------------------------------------------------------

const { auth, user } = endpoints;

// code登录
export const codeLogin = (data) => request.post(auth.codeLogin, data);
// 验证码登录/注册
export const customerRegister = (data) => request.post(auth.register, data);
// 微信授权登录/注册
export const customerRegister2 = (data) => request.post(auth.register2, data);
// 手机密码登录
export const checkLogin = (data) => request.post(auth.checkLogin, data);

// ------------------------------------------------------------------------------------------

// 发送验证码
export const sendVerifyCode = (data) => request.post(auth.sendVerifyCode, data);
// 发送验证码-更换手机号
export const sendVerifyCode2 = (data) => request.post(auth.sendVerifyCode2, data);

// 保存用户修改信息
export const userUpdate = (data) => request.post(user.userUpdate, data);
// 保存更换手机号
export const changeMobile = (data) => request.post(user.changeMobile, data);
// 消息模板列表
export const templateList = (data) => request.post(user.templateList, data);
// 保存用户订阅的消息ID
export const saveSubscribeMessage = (data) => request.post(user.subscribeMessage, data);
