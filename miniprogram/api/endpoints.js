/**
 * 权限相关
 */
const auth = {
  codeLogin: '/sys/weixinMini/customerCodeLogin', // 客户小程序code登录
  register: '/sys/weixinMini/customerRegister', // 客户(小程序)注册并登录(手机号+验证码)
  register2: '/sys/weixinMini/customerRegister2', // 客户(小程序)注册并登录(用户授权获取微信绑定的手机号)
  checkLogin: '/sys/login/check', // 手机号验证码登录
  logout: '/sys/login/logout',
  //
  sendVerifyCode: '/common/sms/sendVerifyCode', // 发送验证码
  verifyCode: '/common/sms/verifyCode', // 验证手机验证码
  sendVerifyCode2: '/sys/user/sendVerifyCode2', // 发送验证码-更换手机号
};

// ----------------------------------------------------------------------

/**
 * 用户相关
 */
const user = {
  changeMobile: '/sys/user/changeMobile', // 保存更换手机号
  sendVerifyCode: '/sys/user/sendVerifyCode2', // 发送验证码-更换手机号
  userUpdate: '/sys/user/userUpdate', // 保存用户修改信息
  // message
  templateList: '/message/weixin/templateList', // 消息模板列表
  subscribeMessage: '/message/weixin/subscribeMessage', // 保存用户订阅的消息ID
};

// ----------------------------------------------------------------------

/**
 *  经营品类
 */
const shop = {
  businessCategoryList: '/shop/businessCategory/list', // 经营品类列表
  businessCategoryListAll: '/shop/businessCategory/listAll', // 全部-经营品类列表
};

// ----------------------------------------------------------------------

/**
 *  活动管理
 */
const activity = {
  activityPage: '/act/activity/activityPage', // 主页-活动列表(分页)
  activityDetail2: '/act/activity/detail2', // 活动详情-for显示
  saveActivityShare: '/act/activity/saveActivityShare', // 保存活动(店员)分享浏览记录关系
  saveActivityCouponShare: '/act/activity/saveActivityCouponShare', // 保存活动的优惠券(好友)分享浏览记录关系
};

// ----------------------------------------------------------------------

/**
 *  订单
 */
const order = {
  createActivityOrder: '/oms/order/createActivityOrder', // 创建活动订单并发起预支付
  orderDetail: '/oms/order/orderDetail', // 活动订单详情
  orderList: '/oms/order/orderList', // 活动订单列表
  orderRefund: '/oms/order/orderRefund', // 订单退款
};

// ----------------------------------------------------------------------

/**
 *  优惠券
 */
const coupon = {
  couponList: '/act/coupon/couponList', // 用户优惠券列表
  receiveCoupon: '/act/coupon/receiveCoupon', // 领取赠送的优惠券
  couponShopList: '/act/coupon/shopList', // 用户有优惠券的-门店列表
  usedCouponList: '/act/coupon/usedCouponList', // 已经使用优惠券列表
  usedCouponDetail: '/act/coupon/usedCouponDetail', // 已经使用优惠券详情
};

// ----------------------------------------------------------------------

/**
 * 支付
 */
const pay = {
  cashier: '/oms/pay/cashier', // 收银台（订单发起预支付）
  payStatus: '/oms/pay/payStatus', // 订单支付状态
};

// ----------------------------------------------------------------------

/**
 * 公共
 */
const common = {
  uploadImage: '/common/upload/image',
  allEnumList: '/common/enum/listAll',
  // allAddressList: '/common/address/listAll',
};

// ----------------------------------------------------------------------

export default { auth, user, shop, activity, order, coupon, pay, common };
