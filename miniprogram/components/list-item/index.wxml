<view class="list-item" bindtap="onTap">
  <text class="label">{{ label }}</text>
  <view class="right">
    <view wx:if="{{ value }}" class="value" style="color: {{ color }}">{{ value }}</view>
    <view wx:elif="{{ !value && placeholder }}" class="value text-placeholder">
      {{ placeholder }}
    </view>
    <image wx:if="{{ isLink }}" class="ic" src="/assets/image/icon/arrow-right.png" />
  </view>
</view>
