import { drawQrcode } from './simple-qrcode.js'; // 我来提供这个轻量二维码生成函数

Component({
  properties: {
    text: {
      type: String,
      value: '',
    },
    size: {
      type: Number,
      value: 200,
    },
    canvasId: {
      type: String,
      value: 'qrcode-canvas',
    },
  },
  data: {
    hasDrawn: false,
  },

  observers: {
    text() {
      this.drawQrcode();
    },
  },

  lifetimes: {
    ready() {
      wx.onAppShow(() => {
        console.log('onAppShow');
        if (this.data.hasDrawn) {
          this.drawQrcode(); // 从后台返回时重绘
        }
      });
    },
    detached() {
      console.log('移除监听，避免内存泄漏');
      // 移除监听，避免内存泄漏
      wx.offAppShow();
    },
  },

  methods: {
    drawQrcode() {
      console.log('drawQrcode');
      if (!this.data.text) return;

      const query = this.createSelectorQuery();
      query
        .select(`#${this.data.canvasId}`)
        .fields({ node: true, size: true })
        .exec((res) => {
          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');

          // 设置画布尺寸
          const size = this.data.size;
          canvas.width = size;
          canvas.height = size;

          // 使用简易二维码绘制
          drawQrcode(ctx, this.data.text, size);
          // 标记已绘制
          this.setData({ hasDrawn: true });
          // 延迟一点时间，确保绘制完成
          setTimeout(() => {
            const base64 = canvas.toDataURL('image/png');
            // 存储到 data 或触发事件回传
            // this.setData({ base64Image: base64 });

            // 如果你要传给页面：
            this.triggerEvent('onGenerated', { base64 });
          }, 100); // 确保绘制完成，100ms 足够
        });
    },
  },
});
