import QRCode from './qrcode-core.js'; // 核心库

export function drawQrcode(ctx, text, size) {
  const qrcode = new QRCode(-1, 'H');
  qrcode.addData(text);
  qrcode.make();

  const cells = qrcode.getModuleCount();
  const tileW = size / cells;
  const tileH = size / cells;

  ctx.fillStyle = '#ffffff';  // 设置白色背景
  ctx.fillRect(0, 0, size, size);  // 清空画布

  ctx.fillStyle = '#000000';
  for (let row = 0; row < cells; row++) {
    for (let col = 0; col < cells; col++) {
      if (qrcode.isDark(row, col)) {
        const w = Math.ceil((col + 1) * tileW) - Math.floor(col * tileW);
        const h = Math.ceil((row + 1) * tileH) - Math.floor(row * tileH);
        ctx.fillRect(Math.round(col * tileW), Math.round(row * tileH), w, h);
      }
    }
  }
}
