.load-more {
  padding: 20rpx 0;
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.tip-text,
.end-text {
  font-size: 26rpx;
  color: #666;
}

.end-text {
  padding: 20rpx 0;
}
.loader {
  margin-right: 20rpx;
  .dot-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10rpx;
    height: 100rpx;
  }

  .dot {
    width: 16rpx;
    height: 16rpx;
    background-color: @brand-color;
    border-radius: 50%;
    animation: bounce 1.2s infinite ease-in-out;
  }

  .dot1 {
    animation-delay: 0s;
  }

  .dot2 {
    animation-delay: 0.2s;
  }

  .dot3 {
    animation-delay: 0.4s;
  }
}

@keyframes bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.3;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
