<!-- prettier-ignore -->
<view class="page" style="transform: translateX({{ drawerVisible ? '600rpx' : '0' }});">
  <image class="top-bg" src="/assets/image/bg/top-gradient.png" />
  <view wx:if="{{ drawerVisible }}" class="page-mask" bindtap="onCloseDrawer"></view>
  <t-navbar title="有券码" class="custom-navbar {{ drawerVisible ? 'custom-navbar-hidden' : '' }}">
    <view
      wx:if="{{ userInfo }}"
      slot="left"
      class="flex items-center nav-left"
      catchtap="toggleDrawer"
    >
      <image class="ic" src="/assets/image/icon/drawer.png" />
      <text class="truncate text">{{ currentCateItem.name || '分类选择' }}</text>
    </view>
    <view wx:else slot="left" class="flex items-center nav-left" catchtap="onTapLogin">
      <image class="ic" src="/assets/image/icon/drawer.png" />
      <text class="text">未登录</text>
    </view>
  </t-navbar>
  <swiper wx:if="{{ list.length > 0 }}" class="swiper-container" circular="{{ list.length > 2 }}" layout-type="tinder">
    <swiper-item
      wx:for="{{ list }}"
      wx:key="index"
      class="swiper-item"
      data-item="{{ item }}"
      bindtap="onTapCard"
    >
      <view class="cover">
        <custom-image src="{{ item.imageUrl }}" mode="aspectFill" />
      </view>
      <view class="content">
        <view class="flex items-center justify-between name">
          <text class="truncate text">{{ item.shopName }}</text>
          <text class="distance">{{ item.distance }}km</text>
        </view>
        <view class="flex items-center justify-between address">
          <view class="flex items-center left">
            <image class="ic" src="/assets/image/icon/address.png" />
            <text class="truncate text">{{ item.address }}</text>
          </view>
          <view class="tag">{{ item.cityName }} ｜ {{ item.mediumCategoryName }}</view>
        </view>
        <view class="line"></view>
        <text class="truncate product-name">{{ item.title }}（价值¥{{ item.totalRetailPrice }}）</text>
        <view class="tips">{{ item.usedLimitsText }}</view>
        <view class="price">
          <text class="symbol">¥</text>
          <text class="val">{{ item.activityPrice }}</text>
          <text class="origin">零售价¥{{ item.totalRetailPrice }}</text>
        </view>
        <!-- 活动状态：1-待发布;2-推广中;3-已下架, 详见枚举 ActActivityStatus	 -->
        <view wx:if="{{ item.status == 2 }}" class="btn btn-buy"> 立即购买 </view>
        <view wx:else class="btn btn-sold-out">已售罄</view>
        <!-- {{ item.statusText }} -->
      </view>
    </swiper-item>
  </swiper>
  <view wx:else class="placeholder-box">
    <image class="img" src="/assets/image/illus/coupon.png" />
    <text class="text">暂无记录</text>
  </view>
</view>

<!-- 抽屉 -->
<view
  class="drawer"
  style="transform: translateX({{ drawerVisible ? '0' : '-600rpx' }});"
  bindtouchstart="onTouchStart"
  bindtouchmove="onTouchMove"
  bindtouchend="onTouchEnd"
>
  <view class="drawer-content">
    <text class="title">分类选择</text>
    <text class="subtitle">有券用户，专享特价购券</text>
    <view
      wx:for="{{ blockCateList }}"
      wx:key="id"
      class="btn-block {{ currentCateItem.id === item.id ? 'btn-block-active' : '' }}"
      data-item="{{ item }}"
      bindtap="chooseCate"
    >
      {{ item.name }}
    </view>
    <view class="cate-box">
      <view
        wx:for="{{ otherCateList }}"
        wx:key="id"
        class="cate-item {{ item.id === currentCateItem.id ? 'cate-item-active' : '' }}"
        data-item="{{ item }}"
        bindtap="chooseCate"
      >
        <custom-image
          wx:if="{{ item.icon }}"
          class="icon"
          src="{{ item.id === currentCateItem.id ? item.icon2 : item.icon }}"
        />
        <view wx:else class="icon"></view>
        <text class="text">{{ item.name }}</text>
      </view>
      <view class="cate-item cate-item-placeholder"></view>
    </view>
  </view>
</view>

<!-- 自定义弹窗 -->
<custom-dialog visible="{{ showAgreeDialog }}">
  <view slot="content">
    <view class="custom-dialog-content">
      <view class="title">协议与隐私政策</view>
      <view class="desc">
        欢迎加入有券！我们根据最新的法律法规、监管政策邀请，更新了《用户协议》和《隐私政策》，请您认真阅读。
      </view>
      <view class="flex items-center agree-block">
        <view class="flex items-center btn-checked" bindtap="onChangeAgree">
          <image class="ic" src="{{ isAgree ? ic_checked : ic_unchecked }}" />
          <text>同意</text>
        </view>
        <text class="btn-text" bindtap="goPolicy">《有券隐私政策》</text>
        <text class="btn-text" bindtap="goAgreement">《用户协议》</text>
      </view>
      <view class="btn-confirm" bindtap="onConfirmAgree">确定</view>
      <view class="btn-cancel" bindtap="onCancelAgree">不同意</view>
    </view>
  </view>
</custom-dialog>
