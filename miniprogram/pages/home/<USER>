.page {
  position: relative;
  width: 100vw;
  height: 100vh;
  background-color: #eaebe4;
  transition: transform 0.3s ease; /* 动画效果 */
  .page-mask {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    background-color: rgba(0, 0, 0, 0.2);
  }
  .top-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 416rpx;
  }
  .nav-left {
    height: 100%;
    .ic {
      width: 30rpx;
      height: 30rpx;
    }
    .text {
      flex: 1;
      height: 35rpx;
      font-weight: 500;
      font-size: 28rpx;
      color: #333;
      margin-left: 16rpx;
    }
  }

  .swiper-container {
    width: 100%;
    height: 88%;
    .swiper-item {
      border-radius: 28rpx;
      margin-top: -100rpx;
      background-color: #fff;
      box-shadow: 0 0 60rpx 6rpx rgba(202, 202, 202, 0.38), 0 4rpx 6rpx 0 rgba(0, 0, 0, 0.06);
      .cover {
        width: 100%;
        height: calc(100% - 378rpx);
        border-top-left-radius: 28rpx;
        border-top-right-radius: 28rpx;
        overflow: hidden;
      }
      .content {
        position: relative;
        padding: 48rpx 24rpx 24rpx;
        .name {
          height: 40rpx;
          line-height: 40rpx;
          .text {
            max-width: 80%;
            font-weight: 500;
            font-size: 40rpx;
            color: #333;
          }
          .distance {
            height: 24rpx;
            font-size: 24rpx;
            color: #333;
          }
        }
        .address {
          height: 24rpx;
          line-height: 24rpx;
          margin-top: 24rpx;
          .left {
            width: 60%;
            .ic {
              min-width: 24rpx;
              width: 24rpx;
              height: 24rpx;
            }
            .text {
              font-size: 24rpx;
              color: #666;
              margin-left: 8rpx;
            }
          }
          .tag {
            width: 300rpx;
            // flex: 1;
            text-align: right;
            font-size: 24rpx;
            color: #999;
          }
        }
        .line {
          height: 1rpx;
          margin-top: 32rpx;
          background-color: #e3e4e9;
        }
        .product-name {
          height: 32rpx;
          font-size: 32rpx;
          color: #333;
          line-height: 32rpx;
          margin-top: 32rpx;
        }
        .tips {
          height: 24rpx;
          font-size: 24rpx;
          color: #666;
          line-height: 24rpx;
          margin-top: 24rpx;
        }
        .price {
          display: flex;
          align-items: flex-end;
          height: 40rpx;
          color: #fa5151;
          margin-top: 24rpx;
          .symbol {
            font-size: 24rpx;
          }
          .val {
            font-family: Roboto, Roboto;
            font-weight: bold;
            font-size: 40rpx;
            line-height: 40rpx;
            margin-left: 2rpx;
            margin-right: 8rpx;
          }
          .origin {
            height: 22rpx;
            font-size: 22rpx;
            color: #999;
            line-height: 22rpx;
            text-decoration: line-through;
          }
        }
        .btn {
          position: absolute;
          right: 28rpx;
          bottom: 24rpx;
          width: 160rpx;
          height: 56rpx;
          line-height: 56rpx;
          font-weight: 500;
          text-align: center;
          font-size: 24rpx;
          border-radius: 48rpx;
        }
        .btn-buy {
          color: #333;
          background: linear-gradient(to right, #ffff82, #bafd8f);
        }
        .btn-sold-out {
          color: #999;
          background-color: #efefef;
        }
      }
    }
  }

  .placeholder-box {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    width: 100%;
    height: 80%;
    .img {
      width: 378rpx;
      height: 308rpx;
    }
    .text {
      height: 32rpx;
      font-size: 32rpx;
      color: #333;
      line-height: 32rpx;
      margin-top: 48rpx;
    }
  }

  .loading-box {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 80%;
    .loading-content {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      .loading-spinner {
        width: 60rpx;
        height: 60rpx;
        border: 4rpx solid #f3f3f3;
        border-top: 4rpx solid #54d601;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      .loading-text {
        margin-top: 32rpx;
        font-size: 28rpx;
        color: #666;
      }
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .error-box {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    width: 100%;
    height: 80%;
    .img {
      width: 378rpx;
      height: 308rpx;
    }
    .text {
      height: 32rpx;
      font-size: 32rpx;
      color: #666;
      line-height: 32rpx;
      margin-top: 48rpx;
    }
    .retry-btn {
      margin-top: 32rpx;
      padding: 16rpx 48rpx;
      background: linear-gradient(to right, #ffff82, #bafd8f);
      border-radius: 48rpx;
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }
  }
}

/* 抽屉 */
.drawer {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1;
  width: 600rpx;
  height: 100%;
  background-color: #354829;
  box-shadow: 0 -4rpx 20rpx 0 rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
  .drawer-content {
    padding: 120rpx 24rpx 16rpx;

    .title {
      height: 32rpx;
      font-weight: 500;
      font-size: 34rpx;
      color: #fff;
      line-height: 32rpx;
    }
    .subtitle {
      height: 24rpx;
      font-size: 24rpx;
      color: #fff;
      line-height: 24rpx;
      margin-top: 24rpx;
      margin-bottom: 32rpx;
    }
    .btn-block {
      width: 100%;
      height: 96rpx;
      line-height: 96rpx;
      text-align: center;
      font-size: 32rpx;
      color: #fff;
      border-radius: 28rpx;
      margin-bottom: 16rpx;
      background-color: rgba(255, 255, 255, 0.3);
      transition: all 0.2s ease;
    }

    .btn-block-active {
      color: #354829;
      background-color: #fff;
    }

    .cate-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;
      .cate-item {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        width: 164rpx;
        height: 144rpx;
        border-radius: 28rpx;
        margin-bottom: 20rpx;
        color: #fff;
        transition: all 0.2s ease;
        .icon {
          width: 32rpx;
          height: 32rpx;
        }
        .text {
          height: 24rpx;
          font-size: 24rpx;
          line-height: 24rpx;
          text-align: center;
          margin-top: 20rpx;
        }
      }
      .cate-item-placeholder {
        height: 0;
        visibility: hidden;
      }
      .cate-item-active {
        color: #354829;
        background-color: #fff;
      }
    }
  }
}

.custom-dialog-content {
  padding: 64rpx 48rpx 36rpx;
  .title {
    line-height: 48rpx;
    text-align: center;
    font-weight: 500;
    font-size: 34rpx;
    color: #000;
  }
  .desc {
    font-size: 34rpx;
    color: #000;
    line-height: 48rpx;
    margin: 48rpx 0;
  }
  .agree-block {
    .btn-checked {
      .ic {
        width: 28rpx;
        height: 28rpx;
        margin-right: 8rpx;
      }
    }
    .btn-text {
      font-weight: 500;
      font-size: 28rpx;
      color: #54d601;
      line-height: 28rpx;
      margin-right: 10rpx;
    }
  }

  .btn-confirm {
    width: 100%;
    line-height: 96rpx;
    text-align: center;
    border-radius: 48rpx;
    margin-top: 22rpx;
    font-size: 32rpx;
    color: #000;
    background: linear-gradient(to right, #ffff82, #bafd8f);
  }
  .btn-cancel {
    width: 120rpx;
    font-size: 24rpx;
    color: #999999;
    line-height: 24rpx;
    text-align: center;
    margin: 20rpx auto 0;
    padding: 12rpx;
  }
}
