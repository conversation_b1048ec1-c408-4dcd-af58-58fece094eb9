import { setAcceptTermsAndPrivacy, getAcceptTermsAndPrivacy } from '@/utils/auth';
// import { getDeepestCategories } from '@/utils/util';
import { activityPage, businessCategoryListAll } from '@/api/index';

const app = getApp();

Page({
  data: {
    drawerVisible: false, // 抽屉是否显示
    startX: 0, // 触摸起始位置
    currentX: 0, // 当前触摸位置
    isSwiping: false, // 是否正在滑动
    //
    userInfo: null,
    list: [],
    currentCateId: 0,
    currentCateItem: {
      id: null,
      level: 0,
      name: '默认推荐',
    },
    showAgreeDialog: !getAcceptTermsAndPrivacy(),
    isAgree: true,
    ic_checked: '/assets/image/icon/check.svg',
    ic_unchecked: '/assets/image/icon/uncheck.svg',
    // largeCategory: null,
    // mediumCategory: null,
    // smallCategory: null,
    allCategoryList: [],
    blockCateList: [],
    otherCateList: [],
    latitude: null,
    longitude: null,
    pageNo: 1,
    pageSize: 50,
    // 页面状态管理
    // isPageLoading: true, // 页面是否正在加载
    // hasInitialized: false, // 是否已完成初始化
    // hasError: false, // 是否有错误
  },
  // onLoad() {
  // this.init();
  // },
  onShow() {
    this.init();
  },
  async init() {
    console.log('=== 开始初始化首页 ===');
    const login = await app.ensureLogin(); // 确保登录
    console.log('登录结果:', login);

    if (login.success) {
      this.setData({ userInfo: login.data });
      console.log('设置用户信息:', login.data);
      this.getUserCity();
      this.getCategoryData();
    } else {
      // 登录失败时也尝试获取数据
      console.log('登录失败，使用默认状态');
      this.setData({ userInfo: null });
      this.getUserCity();
      this.getCategoryData();
    }
  },
  // 获取用户城市
  getUserCity() {
    // 检查用户是否已授权地理位置
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.userLocation']) {
          // 已授权，直接获取位置
          this.getLocation();
        } else {
          // 未授权，请求授权
          wx.authorize({
            scope: 'scope.userLocation',
            success: () => {
              // 授权成功，获取位置
              this.getLocation();
            },
            fail: () => {
              // 授权失败，直接使用默认位置获取数据
              this.setData(
                {
                  latitude: null,
                  longitude: null,
                },
                () => {
                  this.getHomeInfo();
                }
              );
            },
          });
        }
      },
      fail: () => {
        // 获取设置失败，直接使用默认位置
        this.setData(
          {
            latitude: null,
            longitude: null,
          },
          () => {
            this.getHomeInfo();
          }
        );
      },
    });
  },
  // 获取用户经纬度
  getLocation() {
    wx.getLocation({
      type: 'wgs84', // 返回GPS经纬度
      success: (res) => {
        app.globalData.locationInfo = res;
        const params = {
          latitude: res.latitude,
          longitude: res.longitude,
        };
        this.setData(params, () => this.getHomeInfo());
      },
      fail: (err) => {
        console.error('获取位置失败', err);
        wx.showToast({
          title: '获取位置失败，使用默认位置',
          icon: 'none',
        });
        // 位置获取失败时，使用默认位置或不依赖位置的数据获取
        this.setData(
          {
            latitude: null,
            longitude: null,
          },
          () => {
            // 即使没有位置信息也尝试获取数据
            this.getHomeInfo();
          }
        );
      },
    });
  },
  async getHomeInfo(params = {}) {
    console.log('=== 开始获取首页数据 ===');
    wx.showLoading();
    const { currentCateItem, latitude, longitude, pageNo, pageSize } = this.data;

    console.log('请求参数:', {
      latitude,
      longitude,
      largeCategory: currentCateItem.id,
      pageNo,
      pageSize,
      ...params,
    });

    const res = await activityPage({
      latitude,
      longitude,
      largeCategory: currentCateItem.id,
      pageNo,
      pageSize,
      ...params,
    });

    console.log('API响应:', res);

    if (res.success) {
      const newList = res.data?.list || [];
      const list = newList.map((e) => ({
        ...e,
        totalRetailPrice: (e.retailPrice * e.quantity).toFixed(2),
      }));

      console.log('处理后的数据:', list);
      // this.setData({ list: [...list, ...[list[0]]] });
      this.setData({ list });
      console.log('数据设置完成，当前list长度:', this.data.list.length);
    } else {
      console.error('API调用失败:', res);
    }
    wx.hideLoading({ noConflict: true });
  },
  async getCategoryData() {
    const { cityId } = app.globalData.userInfo || {};
    if (cityId) {
      const res = await businessCategoryListAll({ cityId });
      if (res.success && res.data) {
        // const data = getDeepestCategories(res.data);
        const data = res.data;
        const blockCateList = data.slice(0, 2);
        const otherCateList = data.slice(2);
        this.setData({
          blockCateList: [{ id: null, name: '默认推荐' }, ...blockCateList],
          otherCateList,
        });
      }
    }
  },
  onConfirm() {
    this.setData({ showDialog: false });
  },
  onCancel() {
    this.setData({ showDialog: false });
  },
  // 切换抽屉状态（点击触发）
  async toggleDrawer() {
    this.setData({
      drawerVisible: !this.data.drawerVisible,
    });
  },
  async onTapLogin() {
    const login = await app.checkLogin();
    if (login.success) {
      this.init();
    }
  },
  chooseCate(e) {
    const { item } = e.currentTarget.dataset;

    if (!item) return; // 防止 item 为 undefined

    this.setData({ currentCateItem: item }, () => {
      this.getHomeInfo();
      this.toggleDrawer();
    });
  },

  onTapCard(e) {
    const { item } = e.currentTarget.dataset;
    const { latitude, longitude } = this.data;

    wx.navigateTo({
      url: `/subpages/home/<USER>/index?id=${item.id}&latitude=${latitude}&longitude=${longitude}`,
    });
  },
  onChangeAgree() {
    this.setData({ isAgree: !this.data.isAgree });
  },
  goPolicy() {
    wx.navigateTo({ url: '/subpages/mine/policy/index' });
  },
  goAgreement() {
    wx.navigateTo({ url: '/subpages/mine/agreement/index' });
  },
  onConfirmAgree() {
    if (!this.data.isAgree) return;
    setAcceptTermsAndPrivacy();

    this.setData({ showAgreeDialog: false });
  },
  onCancelAgree() {
    wx.exitMiniProgram();
  },
  // === 交互 ===
  // 触摸开始
  onTouchStart(e) {
    this.setData({
      startX: e.touches[0].clientX,
      isSwiping: true,
    });
  },

  // 触摸移动
  onTouchMove(e) {
    const currentX = e.touches[0].clientX;
    const deltaX = currentX - this.data.startX;

    // 只允许水平滑动，限制一定范围内的滑动响应
    if (deltaX > 50 && !this.data.drawerVisible) {
      this.setData({
        drawerVisible: true,
        isSwiping: false,
      });
    } else if (deltaX < -50 && this.data.drawerVisible) {
      this.setData({
        drawerVisible: false,
        isSwiping: false,
      });
    }
  },

  // 触摸结束
  onTouchEnd() {
    this.setData({ isSwiping: false });
  },
  onCloseDrawer() {
    this.setData({
      drawerVisible: false,
    });
  },
  // 重试加载数据
  // onRetry() {
  //   this.setData({
  //     hasError: false,
  //     hasInitialized: false,
  //   });
  //   this.init();
  // },
});
// checkAuth() {
//   if (!wx.getPrivacySetting || !wx.requirePrivacyAuthorize) {
//     // 基础库版本过低，无法使用隐私接口
//     console.log('基础库版本过低，无法使用隐私接口');

//     // this.showPrivacyDialog();
//     return;
//   }

//   wx.getPrivacySetting({
//     success: (res) => {
//       console.log('getPrivacySetting', res);

//       if (res.needAuthorization) {
//         // 需要用户授权隐私政策
//         // this.showPrivacyDialog();
//       } else {
//         // 用户已同意，继续正常流程
//         // this.startApp();
//       }
//     },
//     fail: () => {
//       // 获取隐私设置失败，保守处理
//       // this.showPrivacyDialog();
//     },
//   });

//   wx.onNeedPrivacyAuthorization((resolve) => {
//     console.log('checkAuth-resolve', resolve);

//     // 需要用户同意隐私授权时
//     // 弹出开发者自定义的隐私授权弹窗
//     this.setData({
//       showPrivacy: true,
//     });
//     this.resolvePrivacyAuthorization = resolve;
//   });

//   wx.requirePrivacyAuthorize({
//     success: () => {
//       // 用户同意授权
//       // 继续小程序逻辑
//     },
//     fail: () => {
//       wx.exitMiniProgram();
//     }, // 用户拒绝授权
//     complete: () => {},
//   });
// },
